import React from 'react';
import { Calendar, Clock, Tv } from 'lucide-react';
import { Match } from '../../types';

interface MatchCardProps {
  match: Match;
}

const MatchCard: React.FC<MatchCardProps> = ({ match }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'live':
        return 'bg-red-500 text-white';
      case 'upcoming':
        return 'bg-blue-500 text-white';
      case 'finished':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'live':
        return 'مباشر الآن';
      case 'upcoming':
        return 'قريباً';
      case 'finished':
        return 'انتهت';
      default:
        return status;
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-700 hover:border-slate-600">
      <div className="flex items-center justify-between mb-3">
        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(match.status)}`}>
          {getStatusText(match.status)}
        </span>
        <div className="flex items-center space-x-2 space-x-reverse text-slate-400 text-sm">
          <Calendar size={14} />
          <span>{match.date}</span>
          <Clock size={14} />
          <span>{match.time}</span>
        </div>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3 space-x-reverse flex-1">
          <img
            src={match.homeTeamLogo}
            alt={match.homeTeam}
            className="w-8 h-8 object-cover rounded"
          />
          <span className="text-white font-medium text-sm">{match.homeTeam}</span>
        </div>
        
        <div className="px-4">
          <span className="text-slate-300 font-bold text-lg">VS</span>
        </div>
        
        <div className="flex items-center space-x-3 space-x-reverse flex-1 justify-end">
          <span className="text-white font-medium text-sm">{match.awayTeam}</span>
          <img
            src={match.awayTeamLogo}
            alt={match.awayTeam}
            className="w-8 h-8 object-cover rounded"
          />
        </div>
      </div>

      <div className="border-t border-slate-700 pt-3">
        <div className="flex items-center justify-between">
          <span className="text-slate-400 text-sm">{match.competition}</span>
          <div className="flex items-center space-x-2 space-x-reverse">
            <Tv size={14} className="text-slate-400" />
            <div className="flex space-x-1 space-x-reverse">
              {match.channels.slice(0, 2).map((channel, index) => (
                <span
                  key={index}
                  className="bg-blue-500 text-white text-xs px-2 py-1 rounded"
                >
                  {channel}
                </span>
              ))}
              {match.channels.length > 2 && (
                <span className="text-slate-400 text-xs">
                  +{match.channels.length - 2}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MatchCard;