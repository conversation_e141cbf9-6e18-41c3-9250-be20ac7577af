import React, { useState, useMemo } from 'react';
import { Search, Filter, Wifi, WifiOff } from 'lucide-react';
import Header from '../components/Layout/Header';
import ChannelCard from '../components/Channel/ChannelCard';
import { useFirestore } from '../hooks/useFirestore';
import { Channel } from '../types';

const Channels: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const { data: channels, loading, error } = useFirestore<Channel>('channels');

  const categories = [
    { id: 'all', name: 'الكل' },
    { id: 'رياضة', name: 'رياضة' },
    { id: 'أفلام', name: 'أفلام' },
    { id: 'أخبار', name: 'أخبار' },
    { id: 'وثائقي', name: 'وثائقي' }
  ];

  const filteredChannels = useMemo(() => {
    return channels.filter(channel => {
      const matchesSearch = channel.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || channel.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [channels, searchTerm, selectedCategory]);

  const liveChannelsCount = channels.filter(channel => channel.isLive).length;

  if (error) {
    return (
      <div className="min-h-screen bg-slate-950">
        <Header title="القنوات التلفزيونية" />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <WifiOff size={48} className="text-slate-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">خطأ في الاتصال</h3>
            <p className="text-slate-400">تعذر تحميل القنوات. تحقق من اتصال الإنترنت.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950">
      <Header title="القنوات التلفزيونية" />
      
      <main className="p-4 pb-20 md:pb-4">
        {/* Status Bar */}
        <div className="bg-slate-900 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Wifi className="text-green-400" size={20} />
              <span className="text-white font-medium">متصل</span>
            </div>
            <div className="text-left">
              <div className="text-white font-bold">{channels.length}</div>
              <div className="text-slate-400 text-sm">قناة متاحة</div>
            </div>
            <div className="text-left">
              <div className="text-red-400 font-bold">{liveChannelsCount}</div>
              <div className="text-slate-400 text-sm">مباشر الآن</div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-6 space-y-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <input
              type="text"
              placeholder="البحث عن قناة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-slate-800 border border-slate-700 rounded-lg pr-10 pl-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
            />
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse overflow-x-auto pb-2">
            <Filter className="text-slate-400 flex-shrink-0" size={20} />
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-blue-500 text-white shadow-lg transform scale-105'
                    : 'bg-slate-800 text-slate-300 hover:bg-slate-700'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-slate-800 rounded-lg overflow-hidden">
                  <div className="aspect-video bg-slate-700"></div>
                  <div className="p-4 space-y-2">
                    <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                    <div className="h-3 bg-slate-700 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Channels Grid */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredChannels.map((channel) => (
              <ChannelCard key={channel.id} channel={channel} />
            ))}
          </div>
        )}
        
        {/* No Results */}
        {!loading && filteredChannels.length === 0 && (
          <div className="text-center py-12">
            <div className="text-slate-400 text-lg mb-2">لا توجد قنوات متطابقة مع البحث</div>
            <p className="text-slate-500">جرب البحث بكلمات مختلفة أو اختر فئة أخرى</p>
          </div>
        )}
      </main>
    </div>
  );
};

export default Channels;