import React from 'react';
import { Link } from 'react-router-dom';
import { Tv, Calendar, TrendingUp, Users } from 'lucide-react';
import Header from '../components/Layout/Header';

const Home: React.FC = () => {
  const stats = [
    { icon: Tv, label: 'قناة متاحة', value: '+200', color: 'from-blue-500 to-blue-600' },
    { icon: Calendar, label: 'مباراة اليوم', value: '15', color: 'from-green-500 to-green-600' },
    { icon: Users, label: 'مشاهد نشط', value: '+50K', color: 'from-purple-500 to-purple-600' },
    { icon: TrendingUp, label: 'دولة', value: '25', color: 'from-orange-500 to-orange-600' },
  ];

  const featuredChannels = [
    { id: 1, name: 'beIN Sports 1 HD', image: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg?auto=compress&cs=tinysrgb&w=400', category: 'رياضة' },
    { id: 2, name: 'Al Jazeera Sport', image: 'https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg?auto=compress&cs=tinysrgb&w=400', category: 'رياضة' },
    { id: 3, name: 'MBC Action', image: 'https://images.pexels.com/photos/1190298/pexels-photo-1190298.jpeg?auto=compress&cs=tinysrgb&w=400', category: 'أفلام' },
  ];

  return (
    <div className="min-h-screen bg-slate-950">
      <Header title="Yacine TV" />
      
      <main className="p-4 pb-20 md:pb-4">
        {/* Hero Section */}
        <div className="relative rounded-2xl overflow-hidden mb-6 bg-gradient-to-r from-blue-900 via-purple-900 to-blue-900 p-8">
          <div className="absolute inset-0 bg-[url('https://images.pexels.com/photos/3945313/pexels-photo-3945313.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1')] bg-cover bg-center opacity-20"></div>
          <div className="relative z-10">
            <h1 className="text-3xl font-bold text-white mb-2">مرحباً بك في Yacine TV</h1>
            <p className="text-blue-100 mb-6">شاهد قنواتك المفضلة والمباريات مباشر بجودة عالية</p>
            <div className="flex space-x-4 space-x-reverse">
              <Link
                to="/channels"
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                تصفح القنوات
              </Link>
              <Link
                to="/matches"
                className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-900 px-6 py-3 rounded-lg font-medium transition-colors"
              >
                جدول المباريات
              </Link>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className={`bg-gradient-to-r ${stat.color} rounded-lg p-4 text-white`}>
              <div className="flex items-center justify-between">
                <div>
                  <stat.icon size={24} className="mb-2 opacity-80" />
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <p className="text-sm opacity-80">{stat.label}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Featured Channels */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-white">القنوات المميزة</h2>
            <Link to="/channels" className="text-blue-400 hover:text-blue-300 text-sm">
              عرض الكل
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {featuredChannels.map((channel) => (
              <Link
                key={channel.id}
                to={`/channel/${channel.id}`}
                className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-slate-800 to-slate-900 hover:from-slate-700 hover:to-slate-800 transition-all duration-300"
              >
                <div className="aspect-video relative">
                  <img
                    src={channel.image}
                    alt={channel.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                  <div className="absolute bottom-4 left-4 right-4">
                    <h3 className="text-white font-semibold text-lg">{channel.name}</h3>
                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded">
                      {channel.category}
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* Quick Links */}
        <section>
          <h2 className="text-2xl font-bold text-white mb-4">الوصول السريع</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link
              to="/channels"
              className="bg-gradient-to-r from-slate-800 to-slate-700 hover:from-slate-700 hover:to-slate-600 rounded-lg p-6 text-white transition-all duration-300 group"
            >
              <Tv size={32} className="text-blue-400 mb-3 group-hover:scale-110 transition-transform" />
              <h3 className="text-xl font-semibold mb-2">قائمة القنوات</h3>
              <p className="text-slate-300">تصفح جميع القنوات المتاحة</p>
            </Link>
            
            <Link
              to="/matches"
              className="bg-gradient-to-r from-slate-800 to-slate-700 hover:from-slate-700 hover:to-slate-600 rounded-lg p-6 text-white transition-all duration-300 group"
            >
              <Calendar size={32} className="text-green-400 mb-3 group-hover:scale-110 transition-transform" />
              <h3 className="text-xl font-semibold mb-2">جدول المباريات</h3>
              <p className="text-slate-300">مواعيد المباريات والقنوات الناقلة</p>
            </Link>
          </div>
        </section>
      </main>
    </div>
  );
};

export default Home;