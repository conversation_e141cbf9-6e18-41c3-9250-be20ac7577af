import React from 'react';
import { Link } from 'react-router-dom';
import { Play, Users } from 'lucide-react';
import { Channel } from '../../types';

interface ChannelCardProps {
  channel: Channel;
}

const ChannelCard: React.FC<ChannelCardProps> = ({ channel }) => {
  return (
    <Link
      to={`/channel/${channel.id}`}
      className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-slate-800 to-slate-900 hover:from-slate-700 hover:to-slate-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
    >
      <div className="aspect-video relative">
        <img
          src={channel.image}
          alt={channel.name}
          className="w-full h-full object-cover"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent" />
        
        {channel.isLive && (
          <div className="absolute top-2 right-2 flex items-center space-x-1 space-x-reverse bg-red-500 text-white px-2 py-1 rounded-full text-xs">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            <span>مباشر</span>
          </div>
        )}
        
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-blue-500 rounded-full p-3 transform scale-0 group-hover:scale-100 transition-transform duration-300">
            <Play size={24} className="text-white" fill="white" />
          </div>
        </div>
      </div>
      
      <div className="p-4">
        <h3 className="text-white font-semibold text-lg mb-1 truncate">{channel.name}</h3>
        <div className="flex items-center justify-between text-slate-400 text-sm">
          <span className="flex items-center space-x-1 space-x-reverse">
            <Users size={14} />
            <span>{Math.floor(Math.random() * 1000) + 100} مشاهد</span>
          </span>
          <span className="bg-slate-700 px-2 py-1 rounded text-xs">{channel.category}</span>
        </div>
      </div>
    </Link>
  );
};

export default ChannelCard;