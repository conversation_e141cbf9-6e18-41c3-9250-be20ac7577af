export interface Channel {
  id: string;
  name: string;
  image: string;
  category: string;
  streamUrl: string;
  isLive: boolean;
  country: string;
}

export interface Match {
  id: string;
  homeTeam: string;
  awayTeam: string;
  homeTeamLogo: string;
  awayTeamLogo: string;
  date: string;
  time: string;
  competition: string;
  channels: string[];
  status: 'upcoming' | 'live' | 'finished';
}

export interface Category {
  id: string;
  name: string;
  icon: string;
}