import React, { useState, useEffect } from 'react';
import { 
  Settings as SettingsIcon, 
  Moon, 
  Sun, 
  Bell, 
  Download, 
  Trash2, 
  Info,
  Wifi,
  Volume2,
  Monitor
} from 'lucide-react';
import Header from '../components/Layout/Header';

const Settings: React.FC = () => {
  const [darkMode, setDarkMode] = useState(true);
  const [notifications, setNotifications] = useState(false);
  const [autoPlay, setAutoPlay] = useState(true);
  const [dataUsage, setDataUsage] = useState('high');
  const [cacheSize, setCacheSize] = useState('125 MB');

  useEffect(() => {
    // Check notification permission
    if ('Notification' in window) {
      setNotifications(Notification.permission === 'granted');
    }
  }, []);

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setNotifications(permission === 'granted');
    }
  };

  const clearCache = async () => {
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
      setCacheSize('0 MB');
      alert('تم مسح الذاكرة المؤقتة بنجاح');
    }
  };

  const installApp = () => {
    // This would be handled by the browser's install prompt
    alert('يمكنك إضافة التطبيق للشاشة الرئيسية من قائمة المتصفح');
  };

  return (
    <div className="min-h-screen bg-slate-950">
      <Header title="الإعدادات" />
      
      <main className="p-4 pb-20 md:pb-4 space-y-6">
        {/* Display Settings */}
        <section className="bg-slate-900 rounded-lg p-4">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
            <Monitor size={20} />
            <span>إعدادات العرض</span>
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 space-x-reverse">
                {darkMode ? <Moon size={20} className="text-slate-400" /> : <Sun size={20} className="text-slate-400" />}
                <div>
                  <p className="text-white font-medium">الوضع الداكن</p>
                  <p className="text-slate-400 text-sm">حفظ البطارية وحماية العين</p>
                </div>
              </div>
              <button
                onClick={() => setDarkMode(!darkMode)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  darkMode ? 'bg-blue-500' : 'bg-slate-700'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    darkMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">جودة التشغيل</p>
                <p className="text-slate-400 text-sm">تحديد جودة الفيديو الافتراضية</p>
              </div>
              <select
                value={dataUsage}
                onChange={(e) => setDataUsage(e.target.value)}
                className="bg-slate-800 border border-slate-700 text-white rounded-lg px-3 py-2 text-sm"
              >
                <option value="low">منخفضة (360p)</option>
                <option value="medium">متوسطة (720p)</option>
                <option value="high">عالية (1080p)</option>
                <option value="auto">تلقائي</option>
              </select>
            </div>
          </div>
        </section>

        {/* Playback Settings */}
        <section className="bg-slate-900 rounded-lg p-4">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
            <Volume2 size={20} />
            <span>إعدادات التشغيل</span>
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">التشغيل التلقائي</p>
                <p className="text-slate-400 text-sm">تشغيل الفيديو تلقائياً عند فتح القناة</p>
              </div>
              <button
                onClick={() => setAutoPlay(!autoPlay)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoPlay ? 'bg-blue-500' : 'bg-slate-700'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoPlay ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </section>

        {/* Notifications */}
        <section className="bg-slate-900 rounded-lg p-4">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
            <Bell size={20} />
            <span>الإشعارات</span>
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">إشعارات المباريات</p>
                <p className="text-slate-400 text-sm">تنبيه قبل بداية المباريات المهمة</p>
              </div>
              <button
                onClick={requestNotificationPermission}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  notifications ? 'bg-blue-500' : 'bg-slate-700'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    notifications ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </section>

        {/* Storage & Cache */}
        <section className="bg-slate-900 rounded-lg p-4">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
            <Wifi size={20} />
            <span>التخزين والذاكرة</span>
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">حجم الذاكرة المؤقتة</p>
                <p className="text-slate-400 text-sm">البيانات المحفوظة محلياً</p>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-slate-300 text-sm">{cacheSize}</span>
                <button
                  onClick={clearCache}
                  className="bg-red-500 hover:bg-red-600 text-white p-2 rounded-lg transition-colors"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* App Settings */}
        <section className="bg-slate-900 rounded-lg p-4">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
            <Download size={20} />
            <span>التطبيق</span>
          </h2>
          
          <div className="space-y-4">
            <button
              onClick={installApp}
              className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-colors"
            >
              تثبيت التطبيق على الجهاز
            </button>
          </div>
        </section>

        {/* About */}
        <section className="bg-slate-900 rounded-lg p-4">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
            <Info size={20} />
            <span>حول التطبيق</span>
          </h2>
          
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-slate-400">الإصدار:</span>
              <span className="text-white">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">آخر تحديث:</span>
              <span className="text-white">يناير 2024</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">المطور:</span>
              <span className="text-white">Yacine TV Team</span>
            </div>
          </div>
        </section>

        {/* Footer */}
        <div className="text-center text-slate-500 text-sm py-4">
          <p>© 2024 Yacine TV. جميع الحقوق محفوظة.</p>
        </div>
      </main>
    </div>
  );
};

export default Settings;