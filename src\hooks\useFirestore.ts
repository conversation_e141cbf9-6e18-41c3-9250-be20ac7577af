import { useState, useEffect } from 'react';
import { collection, getDocs, onSnapshot, query, orderBy, where, DocumentData } from 'firebase/firestore';
import { db } from '../firebase/config';

export function useFirestore<T extends DocumentData>(
  collectionName: string, 
  orderByField: string = 'name',
  whereClause?: { field: string; operator: any; value: any }
) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let q = query(collection(db, collectionName), orderBy(orderByField));
    
    if (whereClause) {
      q = query(
        collection(db, collectionName), 
        where(whereClause.field, whereClause.operator, whereClause.value),
        orderBy(orderByField)
      );
    }
    
    // For demo purposes, return mock data since Firebase isn't configured
    const loadMockData = () => {
      setTimeout(() => {
        if (collectionName === 'channels') {
          const mockChannels = [
            {
              id: '1',
              name: 'beIN Sports 1 HD',
              image: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg?auto=compress&cs=tinysrgb&w=400',
              category: 'رياضة',
              streamUrl: 'https://www.youtube.com/embed/jfKfPfyJRdk?autoplay=1&mute=1',
              isLive: true,
              country: 'قطر'
            },
            {
              id: '2',
              name: 'Al Jazeera Sport',
              image: 'https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg?auto=compress&cs=tinysrgb&w=400',
              category: 'رياضة',
              streamUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1',
              isLive: true,
              country: 'قطر'
            },
            {
              id: '3',
              name: 'MBC Action',
              image: 'https://images.pexels.com/photos/1190298/pexels-photo-1190298.jpeg?auto=compress&cs=tinysrgb&w=400',
              category: 'أفلام',
              streamUrl: 'https://www.youtube.com/embed/ScMzIvxBSi4?autoplay=1&mute=1',
              isLive: false,
              country: 'الإمارات'
            },
            {
              id: '4',
              name: 'Dubai Sports',
              image: 'https://images.pexels.com/photos/3621104/pexels-photo-3621104.jpeg?auto=compress&cs=tinysrgb&w=400',
              category: 'رياضة',
              streamUrl: 'https://www.youtube.com/embed/M7lc1UVf-VE?autoplay=1&mute=1',
              isLive: true,
              country: 'الإمارات'
            },
            {
              id: '5',
              name: 'National Geographic',
              image: 'https://images.pexels.com/photos/33109/fall-autumn-red-season.jpg?auto=compress&cs=tinysrgb&w=400',
              category: 'وثائقي',
              streamUrl: 'https://www.youtube.com/embed/K4TOrB7at0Y?autoplay=1&mute=1',
              isLive: false,
              country: 'عالمي'
            },
            {
              id: '6',
              name: 'CNN Arabic',
              image: 'https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg?auto=compress&cs=tinysrgb&w=400',
              category: 'أخبار',
              streamUrl: 'https://www.youtube.com/embed/cPAbx5kgCJo?autoplay=1&mute=1',
              isLive: true,
              country: 'عالمي'
            }
          ] as T[];
          setData(mockChannels);
        } else if (collectionName === 'matches') {
          const mockMatches = [
            {
              id: '1',
              homeTeam: 'ريال مدريد',
              awayTeam: 'برشلونة',
              homeTeamLogo: 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg?auto=compress&cs=tinysrgb&w=100',
              awayTeamLogo: 'https://images.pexels.com/photos/46798/the-ball-stadion-football-the-pitch-46798.jpeg?auto=compress&cs=tinysrgb&w=100',
              date: new Date().toISOString().split('T')[0],
              time: '21:00',
              competition: 'الدوري الإسباني',
              channels: ['beIN Sports 1', 'SSC Sport'],
              status: 'live'
            },
            {
              id: '2',
              homeTeam: 'مانشستر سيتي',
              awayTeam: 'ليفربول',
              homeTeamLogo: 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg?auto=compress&cs=tinysrgb&w=100',
              awayTeamLogo: 'https://images.pexels.com/photos/46798/the-ball-stadion-football-the-pitch-46798.jpeg?auto=compress&cs=tinysrgb&w=100',
              date: new Date().toISOString().split('T')[0],
              time: '18:30',
              competition: 'الدوري الإنجليزي',
              channels: ['beIN Sports 2', 'Sky Sports'],
              status: 'upcoming'
            },
            {
              id: '3',
              homeTeam: 'باريس سان جيرمان',
              awayTeam: 'مارسيليا',
              homeTeamLogo: 'https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg?auto=compress&cs=tinysrgb&w=100',
              awayTeamLogo: 'https://images.pexels.com/photos/46798/the-ball-stadion-football-the-pitch-46798.jpeg?auto=compress&cs=tinysrgb&w=100',
              date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
              time: '20:00',
              competition: 'الدوري الفرنسي',
              channels: ['beIN Sports 3'],
              status: 'finished'
            }
          ] as T[];
          setData(mockMatches);
        }
        setLoading(false);
      }, 500);
    };

    // Use mock data for demo
    loadMockData();

    // Uncomment below to use real Firestore when configured
    /*
    const unsubscribe = onSnapshot(q, 
      (snapshot) => {
        const items = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as T[];
        setData(items);
        setLoading(false);
      },
      (err) => {
        setError(err.message);
        setLoading(false);
      }
    );

    return () => unsubscribe();
    */
  }, [collectionName, orderByField, whereClause]);

  return { data, loading, error };
}

// Custom hook for fetching a single document
export function useFirestoreDoc<T extends DocumentData>(collectionName: string, docId: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!docId) {
      setLoading(false);
      return;
    }

    // Mock data for demo
    setTimeout(() => {
      if (collectionName === 'channels') {
        const mockChannel = {
          id: docId,
          name: docId === '1' ? 'beIN Sports 1 HD' : 'Demo Channel',
          image: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg?auto=compress&cs=tinysrgb&w=400',
          category: 'رياضة',
          streamUrl: 'https://www.youtube.com/embed/jfKfPfyJRdk?autoplay=1&mute=1',
          isLive: true,
          country: 'قطر'
        } as T;
        setData(mockChannel);
      }
      setLoading(false);
    }, 300);
  }, [collectionName, docId]);

  return { data, loading, error };
}