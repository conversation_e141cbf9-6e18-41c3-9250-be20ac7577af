import React, { useState, useMemo } from 'react';
import { Calendar, Filter, Clock, WifiOff } from 'lucide-react';
import Header from '../components/Layout/Header';
import MatchCard from '../components/Match/MatchCard';
import { useFirestore } from '../hooks/useFirestore';
import { Match } from '../types';

const Matches: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const { data: matches, loading, error } = useFirestore<Match>('matches', 'date');

  const statusOptions = [
    { id: 'all', name: 'جميع المباريات', count: matches.length },
    { id: 'live', name: 'مباشر الآن', count: matches.filter(m => m.status === 'live').length },
    { id: 'upcoming', name: 'قريباً', count: matches.filter(m => m.status === 'upcoming').length },
    { id: 'finished', name: 'انتهت', count: matches.filter(m => m.status === 'finished').length }
  ];

  const filteredMatches = useMemo(() => {
    return matches.filter(match => {
      const matchesDate = !selectedDate || match.date === selectedDate;
      const matchesStatus = selectedStatus === 'all' || match.status === selectedStatus;
      return matchesDate && matchesStatus;
    });
  }, [matches, selectedDate, selectedStatus]);

  const liveMatches = filteredMatches.filter(match => match.status === 'live');
  const upcomingMatches = filteredMatches.filter(match => match.status === 'upcoming');
  const finishedMatches = filteredMatches.filter(match => match.status === 'finished');

  if (error) {
    return (
      <div className="min-h-screen bg-slate-950">
        <Header title="جدول المباريات" />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <WifiOff size={48} className="text-slate-400 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-white mb-2">خطأ في الاتصال</h3>
            <p className="text-slate-400">تعذر تحميل جدول المباريات. تحقق من اتصال الإنترنت.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950">
      <Header title="جدول المباريات" />
      
      <main className="p-4 pb-20 md:pb-4">
        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-red-900/30 border border-red-500/20 rounded-lg p-3 text-center">
            <div className="text-red-400 font-bold text-lg">{liveMatches.length}</div>
            <div className="text-slate-400 text-xs">مباشر</div>
          </div>
          <div className="bg-blue-900/30 border border-blue-500/20 rounded-lg p-3 text-center">
            <div className="text-blue-400 font-bold text-lg">{upcomingMatches.length}</div>
            <div className="text-slate-400 text-xs">قريباً</div>
          </div>
          <div className="bg-gray-900/30 border border-gray-500/20 rounded-lg p-3 text-center">
            <div className="text-gray-400 font-bold text-lg">{finishedMatches.length}</div>
            <div className="text-slate-400 text-xs">انتهت</div>
          </div>
        </div>

        {/* Date and Status Filter */}
        <div className="mb-6 space-y-4">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse bg-slate-800 rounded-lg px-3 py-2">
              <Calendar className="text-slate-400" size={20} />
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="bg-transparent text-white focus:outline-none"
              />
            </div>
            <button
              onClick={() => setSelectedDate(new Date().toISOString().split('T')[0])}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors"
            >
              اليوم
            </button>
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse overflow-x-auto pb-2">
            <Filter className="text-slate-400 flex-shrink-0" size={20} />
            {statusOptions.map((status) => (
              <button
                key={status.id}
                onClick={() => setSelectedStatus(status.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 flex items-center space-x-2 space-x-reverse ${
                  selectedStatus === status.id
                    ? 'bg-blue-500 text-white shadow-lg transform scale-105'
                    : 'bg-slate-800 text-slate-300 hover:bg-slate-700'
                }`}
              >
                <span>{status.name}</span>
                {status.count > 0 && (
                  <span className="bg-white/20 text-xs px-2 py-1 rounded-full">
                    {status.count}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="animate-pulse bg-slate-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="h-4 bg-slate-700 rounded w-20"></div>
                  <div className="h-4 bg-slate-700 rounded w-32"></div>
                </div>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-8 h-8 bg-slate-700 rounded"></div>
                    <div className="h-4 bg-slate-700 rounded w-24"></div>
                  </div>
                  <div className="h-6 bg-slate-700 rounded w-8"></div>
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="h-4 bg-slate-700 rounded w-24"></div>
                    <div className="w-8 h-8 bg-slate-700 rounded"></div>
                  </div>
                </div>
                <div className="h-4 bg-slate-700 rounded w-40"></div>
              </div>
            ))}
          </div>
        )}

        {/* Live Matches Banner */}
        {!loading && liveMatches.length > 0 && (
          <div className="mb-6 bg-gradient-to-r from-red-900 to-red-800 rounded-lg p-4 border border-red-700">
            <div className="flex items-center space-x-2 space-x-reverse mb-3">
              <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
              <h3 className="text-white font-bold text-lg">المباريات المباشرة الآن</h3>
            </div>
            <div className="grid gap-4 md:grid-cols-2">
              {liveMatches.map(match => (
                <MatchCard key={match.id} match={match} />
              ))}
            </div>
          </div>
        )}

        {/* Upcoming Matches */}
        {!loading && upcomingMatches.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <Clock className="text-blue-400" size={20} />
              <h3 className="text-white font-bold text-lg">المباريات القادمة</h3>
            </div>
            <div className="space-y-4">
              {upcomingMatches.map(match => (
                <MatchCard key={match.id} match={match} />
              ))}
            </div>
          </div>
        )}

        {/* Finished Matches */}
        {!loading && finishedMatches.length > 0 && selectedStatus !== 'live' && selectedStatus !== 'upcoming' && (
          <div className="mb-6">
            <h3 className="text-white font-bold text-lg mb-4">النتائج</h3>
            <div className="space-y-4">
              {finishedMatches.map(match => (
                <MatchCard key={match.id} match={match} />
              ))}
            </div>
          </div>
        )}

        {/* All Other Matches */}
        {!loading && selectedStatus === 'all' && (upcomingMatches.length > 0 || finishedMatches.length > 0) && (
          <div className="space-y-4">
            {filteredMatches
              .filter(match => match.status !== 'live')
              .map(match => (
                <MatchCard key={match.id} match={match} />
              ))}
          </div>
        )}
        
        {/* No Results */}
        {!loading && filteredMatches.length === 0 && (
          <div className="text-center py-12">
            <Calendar size={48} className="text-slate-400 mx-auto mb-4" />
            <div className="text-slate-400 text-lg mb-2">لا توجد مباريات في هذا التاريخ</div>
            <p className="text-slate-500">اختر تاريخ آخر لعرض المباريات المتاحة</p>
          </div>
        )}
      </main>
    </div>
  );
};

export default Matches;