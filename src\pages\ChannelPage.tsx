import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowRight, Share, Heart, Users, Eye } from 'lucide-react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import VideoPlayer from '../components/Video/VideoPlayer';
import { Channel } from '../types';

const ChannelPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [channel, setChannel] = useState<Channel | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [viewerCount] = useState(Math.floor(Math.random() * 5000) + 500);

  useEffect(() => {
    const fetchChannel = async () => {
      if (!id) return;
      
      try {
        // For demo purposes, using mock data. Replace with actual Firebase call
        const mockChannel: Channel = {
          id: id,
          name: id === '1' ? 'beIN Sports 1 HD' : 
                id === '2' ? 'Al Jazeera Sport' :
                id === '3' ? 'MBC Action' :
                id === '4' ? 'Dubai Sports' :
                id === '5' ? 'National Geographic' :
                'CNN Arabic',
          image: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg?auto=compress&cs=tinysrgb&w=800',
          category: id === '1' || id === '2' || id === '4' ? 'رياضة' : 
                   id === '3' ? 'أفلام' : 
                   id === '5' ? 'وثائقي' : 'أخبار',
          streamUrl: `https://www.youtube.com/embed/jfKfPfyJRdk?autoplay=1&mute=1`,
          isLive: Math.random() > 0.3,
          country: 'قطر'
        };
        
        setChannel(mockChannel);
      } catch (error) {
        console.error('Error fetching channel:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchChannel();
  }, [id]);

  const handleShare = async () => {
    if (navigator.share && channel) {
      try {
        await navigator.share({
          title: channel.name,
          text: `شاهد ${channel.name} على Yacine TV`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('تم نسخ الرابط');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="text-slate-400">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!channel) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">القناة غير موجودة</h2>
          <button
            onClick={() => navigate('/channels')}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors"
          >
            العودة للقنوات
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 px-4 py-3 sticky top-0 z-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => navigate(-1)}
              className="text-slate-300 hover:text-white transition-colors"
            >
              <ArrowRight size={24} />
            </button>
            <div>
              <h1 className="text-white text-lg font-bold">{channel.name}</h1>
              <div className="flex items-center space-x-2 space-x-reverse text-sm">
                {channel.isLive && (
                  <div className="flex items-center space-x-1 space-x-reverse text-red-400">
                    <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
                    <span>مباشر</span>
                  </div>
                )}
                <span className="text-slate-400">•</span>
                <span className="text-slate-400">{channel.category}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              onClick={() => setIsLiked(!isLiked)}
              className={`p-2 rounded-full transition-colors ${
                isLiked ? 'text-red-400 bg-red-400/10' : 'text-slate-400 hover:text-white'
              }`}
            >
              <Heart size={20} fill={isLiked ? 'currentColor' : 'none'} />
            </button>
            <button
              onClick={handleShare}
              className="text-slate-400 hover:text-white transition-colors p-2"
            >
              <Share size={20} />
            </button>
          </div>
        </div>
      </header>

      <main className="pb-20 md:pb-4">
        {/* Video Player */}
        <div className="relative">
          <VideoPlayer src={channel.streamUrl} title={channel.name} />
          
          {/* Video Overlay Info */}
          <div className="absolute bottom-4 left-4 right-4 md:hidden">
            <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center justify-between text-white text-sm">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Eye size={16} />
                  <span>{viewerCount.toLocaleString()} مشاهد</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Users size={16} />
                  <span>متصل الآن</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Channel Info */}
        <div className="p-4 space-y-6">
          {/* Stats */}
          <div className="bg-slate-900 rounded-lg p-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-white">{viewerCount.toLocaleString()}</div>
                <div className="text-slate-400 text-sm">مشاهد</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-400">
                  {channel.isLive ? 'مباشر' : 'غير متاح'}
                </div>
                <div className="text-slate-400 text-sm">الحالة</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-400">HD</div>
                <div className="text-slate-400 text-sm">الجودة</div>
              </div>
            </div>
          </div>

          {/* Channel Details */}
          <div className="bg-slate-900 rounded-lg p-4 space-y-4">
            <h2 className="text-xl font-bold text-white">معلومات القناة</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-slate-400">الاسم:</span>
                <span className="text-white font-medium">{channel.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">الفئة:</span>
                <span className="text-white">{channel.category}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">البلد:</span>
                <span className="text-white">{channel.country}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-400">اللغة:</span>
                <span className="text-white">العربية</span>
              </div>
            </div>
          </div>

          {/* Related Channels */}
          <div className="bg-slate-900 rounded-lg p-4">
            <h2 className="text-xl font-bold text-white mb-4">قنوات مشابهة</h2>
            <div className="grid grid-cols-2 gap-3">
              {[
                { id: '1', name: 'beIN Sports 2', category: 'رياضة' },
                { id: '2', name: 'Al Jazeera Sport+', category: 'رياضة' },
                { id: '3', name: 'SSC Sport', category: 'رياضة' },
                { id: '4', name: 'Dubai Sports 2', category: 'رياضة' }
              ].map((relatedChannel) => (
                <button
                  key={relatedChannel.id}
                  onClick={() => navigate(`/channel/${relatedChannel.id}`)}
                  className="bg-slate-800 hover:bg-slate-700 rounded-lg p-3 text-right transition-colors"
                >
                  <div className="text-white font-medium text-sm">{relatedChannel.name}</div>
                  <div className="text-slate-400 text-xs">{relatedChannel.category}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-4 space-x-reverse">
            <button className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-colors">
              إضافة للمفضلة
            </button>
            <button
              onClick={handleShare}
              className="flex-1 bg-slate-700 hover:bg-slate-600 text-white py-3 rounded-lg font-medium transition-colors"
            >
              مشاركة القناة
            </button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ChannelPage;