import React from 'react';
import { Menu, Search, Bell } from 'lucide-react';

interface HeaderProps {
  title: string;
}

const Header: React.FC<HeaderProps> = ({ title }) => {
  return (
    <header className="bg-slate-900 border-b border-slate-700 px-4 py-3 flex items-center justify-between">
      <div className="flex items-center space-x-4 space-x-reverse">
        <button className="text-slate-300 hover:text-white transition-colors">
          <Menu size={24} />
        </button>
        <h1 className="text-white text-xl font-bold">{title}</h1>
      </div>
      
      <div className="flex items-center space-x-3 space-x-reverse">
        <button className="text-slate-300 hover:text-white transition-colors">
          <Search size={20} />
        </button>
        <button className="text-slate-300 hover:text-white transition-colors relative">
          <Bell size={20} />
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
            3
          </span>
        </button>
      </div>
    </header>
  );
};

export default Header;