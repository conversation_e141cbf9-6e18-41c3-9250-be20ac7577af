import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import Channels from './pages/Channels';
import ChannelPage from './pages/ChannelPage';
import Matches from './pages/Matches';
import Settings from './pages/Settings';
import BottomNavigation from './components/Layout/BottomNavigation';

function App() {
  useEffect(() => {
    // Register Service Worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }

    // Request notification permission
    if ('Notification' in window && 'serviceWorker' in navigator) {
      Notification.requestPermission();
    }
  }, []);

  return (
    <Router>
      <div className="app" dir="rtl">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/channels" element={<Channels />} />
          <Route path="/channel/:id" element={<ChannelPage />} />
          <Route path="/matches" element={<Matches />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
        <BottomNavigation />
      </div>
    </Router>
  );
}

export default App;